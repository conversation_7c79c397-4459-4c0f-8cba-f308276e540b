// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./IDex.sol"; // or redefine the interface here if needed


contract MockDex is IDex {
    function swap(address tokenIn, address tokenOut, uint amountIn) external override {
        IERC20(tokenIn).transferFrom(msg.sender, address(this), amountIn);
        IERC20(tokenOut).transfer(msg.sender, amountIn);
    }
}
