import hardhat from "hardhat";

const { ethers } = hardhat;

async function main() {
  const initialSupply = ethers.utils.parseUnits("1000000", 18);

  const TokenA = await ethers.deployContract("MockERC20", [
    "TokenA",
    "TKA",
    initialSupply,
  ]);
  console.log("🟢 TokenA deployed at:", TokenA.address);

  const TokenB = await ethers.deployContract("MockERC20", [
    "TokenB",
    "TKB",
    initialSupply,
  ]);
  console.log("🔵 TokenB deployed at:", TokenB.address);
}

main().catch((err) => {
  console.error("❌ Token deployment failed:", err);
  process.exit(1);
});
