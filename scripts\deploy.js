import pkg from 'hardhat';
const { network, ethers } = pkg;

async function deployContract(name, ...args) {
  console.log(`🔧 Deploying ${name} to ${network.name}...`);
  const factory = await ethers.getContractFactory(name);
  const contract = await factory.deploy(...args);
  await contract.deployed();
  console.log(`✅ ${name} deployed at: ${contract.address}`);
  return contract;
}

async function main() {
  // Example: Deploy Arbitrage contract with constructor args
  const arbitrage = await deployContract("Arbitrage" /*, constructor args */);

  // Optional: Register or log address
  // console.log(`📦 Registered Arbitrage at ${arbitrage.address}`);
}

main().catch((error) => {
  console.error("❌ Deployment failed:", error);
  process.exitCode = 1;
});
