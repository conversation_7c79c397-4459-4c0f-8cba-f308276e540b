
import hre from "hardhat";

async function main() {
  const MockDex = await hre.ethers.getContractFactory("MockDex");

  const dexBuy = await MockDex.deploy(2); // e.g. buy gives double
  await dexBuy.deployed();
  console.log("🟢 MockDexBuy deployed at:", dexBuy.address);

  const dexSell = await MockDex.deploy(1); // e.g. sell returns same
  await dexSell.deployed();
  console.log("🔴 MockDexSell deployed at:", dexSell.address);
}

main().catch((err) => {
  console.error("❌ Deployment failed:", err);
  process.exit(1);
});

