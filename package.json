{"name": "arbitrage-contract", "version": "1.0.0", "type": "module", "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.2.0", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^8.0.2", "@types/mocha": "^10.0.10", "@types/node": "^22.17.2", "chai": "^5.3.1", "ethers": "^5.7.0", "forge-std": "github:foundry-rs/forge-std#v1.9.4", "hardhat": "^2.20.0", "mocha": "^11.7.1", "ts-node": "^10.9.2", "typescript": "~5.8.0"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0"}}